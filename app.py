from flask import Flask, render_template, request, jsonify
from openai import OpenAI
from core.faiss import VectorDatabase
from core.text_processing import (
    extract_and_chunk_pdf,
    extract_and_chunk_docx,
    extract_and_chunk_excel,
    extract_text_from_file,
    get_supported_formats
)
from KEY.DEEPSEEK_KEY import deepseek_key
import os

app = Flask(__name__, template_folder='tamplates')

# 添加错误处理
@app.errorhandler(500)
def internal_error(error):
    """处理500内部服务器错误"""
    return jsonify({
        'error': '内部服务器错误',
        'message': '服务器遇到了一个错误，请稍后重试'
    }), 500

@app.errorhandler(404)
def not_found(error):
    """处理404错误"""
    return jsonify({
        'error': '页面未找到',
        'message': '请求的资源不存在'
    }), 404

# 初始化OpenAI客户端
client = OpenAI(api_key=deepseek_key, base_url="https://api.deepseek.com")

# 初始化向量数据库
vector_db = VectorDatabase()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        # 检查文件格式
        filename = file.filename.lower()
        supported_formats = get_supported_formats()
        if not any(filename.endswith(fmt) for fmt in supported_formats):
            return jsonify({
                'error': f'不支持的文件格式。支持的格式: {", ".join(supported_formats)}'
            }), 400

        # 读取文件内容
        file_content = file.read()

        # 检查是否为Word文档或PDF文档，需要特殊处理
        if filename.endswith(('.doc', '.docx')):
            # Word文档分块处理
            try:
                chunks = extract_and_chunk_docx(file_content, file.filename)

                # 提取所有分块的文本用于向量化
                chunk_texts = [chunk['text'] for chunk in chunks]

                # 添加到向量数据库
                vector_db.add_texts(chunk_texts)

                # 计算统计信息
                total_chars = sum(len(chunk['text']) for chunk in chunks)
                avg_chunk_size = total_chars // len(chunks) if chunks else 0
                overlap_count = sum(1 for chunk in chunks if chunk['has_overlap'])

                return jsonify({
                    'success': True,
                    'filename': file.filename,
                    'file_type': os.path.splitext(filename)[1],
                    'processing_type': 'advanced_chunking',
                    'chunks_added': len(chunks),
                    'total_characters': total_chars,
                    'average_chunk_size': avg_chunk_size,
                    'overlapping_chunks': overlap_count,
                    'message': f'成功处理Word文档 {file.filename}，使用递归+重叠分块，添加 {len(chunks)} 个文本块到向量数据库'
                })

            except Exception as e:
                return jsonify({'error': f'Word文档分块处理失败: {str(e)}'}), 500

        elif filename.endswith('.pdf'):
            # PDF文档高级分块处理
            try:
                chunks = extract_and_chunk_pdf(file_content, file.filename)

                # 提取所有分块的文本用于向量化
                chunk_texts = [chunk['text'] for chunk in chunks]

                # 添加到向量数据库
                vector_db.add_texts(chunk_texts)

                # 计算统计信息
                total_chars = sum(len(chunk['text']) for chunk in chunks)
                avg_chunk_size = total_chars // len(chunks) if chunks else 0
                overlap_count = sum(1 for chunk in chunks if chunk['has_overlap'])

                # 获取处理信息
                sample_chunk = chunks[0] if chunks else {}
                processing_info = sample_chunk.get('metadata', {}).get('pdf_processing_info', {})
                content_types = sample_chunk.get('metadata', {}).get('content_types', {})

                return jsonify({
                    'success': True,
                    'filename': file.filename,
                    'file_type': os.path.splitext(filename)[1],
                    'processing_type': 'pdf_advanced_chunking',
                    'chunks_added': len(chunks),
                    'total_characters': total_chars,
                    'average_chunk_size': avg_chunk_size,
                    'overlapping_chunks': overlap_count,
                    'pdf_processing_info': processing_info,
                    'content_analysis': content_types,
                    'message': f'成功处理PDF文档 {file.filename}，使用PyMuPDF+Camelot+PaddleOCR+递归分块，添加 {len(chunks)} 个文本块到向量数据库'
                })

            except Exception as e:
                return jsonify({'error': f'PDF文档分块处理失败: {str(e)}'}), 500

        elif filename.endswith(('.xlsx', '.xls')):
            # Excel文档高级分块处理
            try:
                chunks = extract_and_chunk_excel(file_content, file.filename)

                # 提取所有分块的文本用于向量化
                chunk_texts = [chunk['text'] for chunk in chunks]

                # 添加到向量数据库
                vector_db.add_texts(chunk_texts)

                # 计算统计信息
                total_chars = sum(len(chunk['text']) for chunk in chunks)
                avg_chunk_size = total_chars // len(chunks) if chunks else 0

                # 获取处理信息
                sample_chunk = chunks[0] if chunks else {}
                processing_stats = sample_chunk.get('metadata', {}).get('excel_processing_stats', {})
                sheet_info = sample_chunk.get('metadata', {}).get('sheet_info', {})

                return jsonify({
                    'success': True,
                    'filename': file.filename,
                    'file_type': os.path.splitext(filename)[1],
                    'processing_type': 'excel_advanced_chunking',
                    'chunks_added': len(chunks),
                    'total_characters': total_chars,
                    'average_chunk_size': avg_chunk_size,
                    'excel_processing_stats': processing_stats,
                    'sheet_info': sheet_info,
                    'processing_pipeline': [
                        '1. Pandas解析Excel',
                        '2. 流式分块',
                        '3. 按行列分块',
                        '4. 语义分块',
                        '5. BGE向量化',
                        '6. FAISS存储'
                    ],
                    'message': f'成功处理Excel文档 {file.filename}，使用Pandas+多级分块+BGE向量化，添加 {len(chunks)} 个文本块到FAISS向量数据库'
                })

            except Exception as e:
                return jsonify({'error': f'Excel文档分块处理失败: {str(e)}'}), 500

        else:
            # 其他文件类型的常规处理
            # 创建一个模拟文件对象用于extract_text_from_file函数
            class MockFile:
                def __init__(self, content, filename):
                    self.content = content
                    self.filename = filename

                def read(self):
                    return self.content

            mock_file = MockFile(file_content, file.filename)
            content = extract_text_from_file(mock_file)

            # 将文件内容按行分割并添加到向量数据库
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            if lines:
                vector_db.add_texts(lines)
                return jsonify({
                    'success': True,
                    'filename': file.filename,
                    'file_type': os.path.splitext(filename)[1],
                    'processing_type': 'line_based',
                    'lines_added': len(lines),
                    'message': f'成功处理 {file.filename}，添加 {len(lines)} 行文本到向量数据库'
                })
            else:
                return jsonify({'error': '文件内容为空或无法提取文本'}), 400

    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        return jsonify({'error': f'处理文件时出错: {str(e)}'}), 500

@app.route('/chat', methods=['POST'])
def chat():
    """处理聊天消息"""
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        response_mode = data.get('mode', 'concise')  # 'concise' 或 'detailed'

        if not user_message:
            return jsonify({'error': '消息不能为空'}), 400

        # 使用向量数据库搜索相关内容
        search_results = vector_db.search(user_message, k=3)

        # 构建上下文
        context = ""
        if search_results:
            context = "\n".join([result['text'] for result in search_results])

        # 根据模式设置不同的提示词
        if response_mode == 'concise':
            system_prompt = """你是一个智能助手。请基于提供的上下文信息，用简洁明了的方式回答用户问题。
            回答要求：
            1. 直接回答问题，不超过50字
            2. 如果上下文中没有相关信息，请说"抱歉，我没有找到相关信息"
            3. 不要添加额外的解释或背景信息"""
        else:  # detailed
            system_prompt = """你是一个智能助手。请基于提供的上下文信息，详细回答用户问题。
            回答要求：
            1. 提供全面详细的回答
            2. 如果上下文中有相关信息，请充分利用
            3. 可以适当展开说明和举例
            4. 如果上下文中没有相关信息，请说"抱歉，我没有找到相关信息"并尝试给出一般性回答"""

        # 构建消息
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"上下文信息：\n{context}\n\n用户问题：{user_message}"}
        ]

        # 调用DeepSeek API
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            temperature=0.7,
            max_tokens=1000 if response_mode == 'detailed' else 200
        )

        reply = response.choices[0].message.content

        return jsonify({
            'reply': reply,
            'context_used': bool(context),
            'mode': response_mode
        })

    except Exception as e:
        return jsonify({'error': f'处理消息时出错: {str(e)}'}), 500

@app.route('/search', methods=['POST'])
def search_vectors():
    """向量搜索接口"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        k = data.get('k', 5)

        if not query:
            return jsonify({'error': '查询内容不能为空'}), 400

        # 执行向量搜索
        results = vector_db.search(query, k=k)

        return jsonify({
            'success': True,
            'query': query,
            'results': results,
            'count': len(results)
        })

    except Exception as e:
        return jsonify({'error': f'搜索时出错: {str(e)}'}), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """获取向量数据库统计信息"""
    try:
        stats = vector_db.get_stats()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': f'获取统计信息时出错: {str(e)}'}), 500

@app.route('/supported-formats', methods=['GET'])
def supported_formats():
    """获取支持的文件格式"""
    try:
        formats = get_supported_formats()
        descriptions = {
            '.txt': '纯文本文件',
            '.pdf': 'PDF文档（高级处理：文本+表格+图片OCR）',
            '.doc': 'Word文档（旧版）',
            '.docx': 'Word文档（新版，高级分块处理）',
            '.csv': 'CSV表格文件',
            '.xlsx': 'Excel文件（Pandas解析+多级分块+BGE向量化）',
            '.xls': 'Excel文件（Pandas解析+多级分块+BGE向量化）'
        }

        return jsonify({
            'supported_formats': formats,
            'descriptions': {fmt: descriptions.get(fmt, '未知格式') for fmt in formats}
        })
    except Exception as e:
        return jsonify({'error': f'获取格式信息时出错: {str(e)}'}), 500

if __name__ == '__main__':
    print("🚀 启动FAISS向量搜索聊天系统...")
    print("📱 请在浏览器中访问: http://localhost:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)