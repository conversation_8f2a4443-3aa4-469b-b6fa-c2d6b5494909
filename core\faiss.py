import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
import pickle
import os

class VectorDatabase:
    def __init__(self, model_name=r'C:\Users\<USER>\Desktop\model\bge-large-zh-v1.5'):
        """
        初始化向量数据库
        """
        self.model = SentenceTransformer(model_name)
        self.dimension = 1024  # bge-large-zh-v1.5 的向量维度
        self.index = faiss.IndexFlatIP(self.dimension)  # 使用内积相似度
        self.texts = []  # 存储原始文本

        # 确保database文件夹存在
        self.database_dir = 'database'
        os.makedirs(self.database_dir, exist_ok=True)

        # 设置文件路径到database文件夹
        self.index_file = os.path.join(self.database_dir, 'faiss_index.bin')
        self.texts_file = os.path.join(self.database_dir, 'texts.pkl')

        # 尝试加载已存在的索引
        self.load_index()

    def add_texts(self, texts):
        """
        添加文本到向量数据库
        """
        if isinstance(texts, str):
            texts = [texts]

        # 生成向量
        embeddings = self.model.encode(texts)

        # 归一化向量（用于内积相似度）
        faiss.normalize_L2(embeddings)

        # 添加到索引
        self.index.add(embeddings.astype('float32'))

        # 保存原始文本
        self.texts.extend(texts)

        print(f"已添加 {len(texts)} 个文本到向量数据库")

        # 保存索引
        self.save_index()

    def search(self, query, k=5):
        """
        搜索相似文本
        """
        if self.index.ntotal == 0:
            return []

        # 生成查询向量
        query_embedding = self.model.encode([query])
        faiss.normalize_L2(query_embedding)

        # 搜索
        scores, indices = self.index.search(query_embedding.astype('float32'), k)

        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx < len(self.texts):
                results.append({
                    'text': self.texts[idx],
                    'score': float(score),
                    'rank': i + 1
                })

        return results

    def save_index(self):
        """
        保存索引到文件
        """
        faiss.write_index(self.index, self.index_file)
        with open(self.texts_file, 'wb') as f:
            pickle.dump(self.texts, f)

    def load_index(self):
        """
        从文件加载索引
        """
        if os.path.exists(self.index_file) and os.path.exists(self.texts_file):
            try:
                self.index = faiss.read_index(self.index_file)
                with open(self.texts_file, 'rb') as f:
                    self.texts = pickle.load(f)
                print(f"已加载索引，包含 {len(self.texts)} 个文本")
            except Exception as e:
                print(f"加载索引失败: {e}")

    def get_stats(self):
        """
        获取数据库统计信息
        """
        return {
            'total_texts': len(self.texts),
            'index_size': self.index.ntotal,
            'dimension': self.dimension
        }

# 示例使用
if __name__ == "__main__":
    # 创建向量数据库实例
    db = VectorDatabase()

    # 示例文本
    sample_texts = [
        "人工智能是计算机科学的一个分支",
        "机器学习是人工智能的子领域",
        "深度学习使用神经网络进行学习",
        "自然语言处理帮助计算机理解人类语言",
        "计算机视觉让机器能够理解图像",
        "Python是一种流行的编程语言",
        "数据科学结合统计学和编程",
        "向量数据库用于存储和搜索高维向量"
    ]

    # 添加文本
    db.add_texts(sample_texts)

    # 搜索示例
    query = "什么是机器学习"
    results = db.search(query, k=3)

    print(f"\n查询: {query}")
    print("搜索结果:")
    for result in results:
        print(f"排名 {result['rank']}: {result['text']} (相似度: {result['score']:.4f})")

    # 显示统计信息
    stats = db.get_stats()
    print(f"\n数据库统计: {stats}")