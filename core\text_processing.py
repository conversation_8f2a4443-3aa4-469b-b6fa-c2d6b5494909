#!/usr/bin/env python3
"""
文档处理模块
包含所有文件处理、分块、向量化相关功能
"""

import os
import re
import tempfile
from io import BytesIO
from typing import List, Dict, Any
import base64

# PDF处理相关导入
import fitz  # PyMuPDF
import camelot
import pdfplumber

# 移除 PaddleOCR 依赖，使用 Qwen2.5-VL 替代
PADDLEOCR_AVAILABLE = False

# Vision处理相关导入
from transformers import AutoModelForVision2Seq, AutoProcessor
import torch
from PIL import Image
from qwen_vl_utils import process_vision_info

# 其他库导入
import docx
import pandas as pd
import openpyxl
from openpyxl import load_workbook

# 初始化图像识别引擎（使用Qwen2.5-VL）
vision_engine = None
vision_engine_type = None

try:
    # 初始化Qwen2.5-VL模型
    model_path = r"C:\Users\<USER>\Desktop\model\models\Qwen\Qwen2.5-VL-3B-Instruct"

    # 加载模型和处理器（优化内存使用）
    qwen_model = AutoModelForVision2Seq.from_pretrained(
        model_path,
        torch_dtype=torch.float16,  # 使用半精度减少内存
        device_map="auto",
        trust_remote_code=True,
        low_cpu_mem_usage=True,     # 减少CPU内存使用
        max_memory={0: "5GB", "cpu": "8GB"}  # 限制内存使用
    )
    qwen_processor = AutoProcessor.from_pretrained(model_path)

    vision_engine = {
        'model': qwen_model,
        'processor': qwen_processor
    }
    vision_engine_type = 'qwen2vl'
    print("✅ Qwen2.5-VL 图像识别引擎初始化成功")
except Exception as e:
    print(f"⚠️ Qwen2.5-VL 初始化失败: {e}")
    vision_engine = None

class DocumentChunker:
    """文档分块处理类"""

    def __init__(self, chunk_size: int = 500, overlap_size: int = 50, max_depth: int = 3):
        """
        初始化分块器

        Args:
            chunk_size: 基础分块大小（字符数）
            overlap_size: 重叠大小（字符数）
            max_depth: 递归分块最大深度
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size
        self.max_depth = max_depth

        # 分割符优先级（从高到低）
        self.separators = [
            "\n\n",  # 段落分隔
            "\n",    # 行分隔
            "。",    # 句号
            "！",    # 感叹号
            "？",    # 问号
            "；",    # 分号
            "，",    # 逗号
            " ",     # 空格
            ""       # 字符级分割
        ]

    def split_text_recursive(self, text: str, depth: int = 0) -> List[str]:
        """
        递归分块文本

        Args:
            text: 要分块的文本
            depth: 当前递归深度

        Returns:
            分块后的文本列表
        """
        if len(text) <= self.chunk_size or depth >= self.max_depth:
            return [text] if text.strip() else []

        # 尝试使用不同的分割符
        for separator in self.separators:
            if separator in text:
                chunks = self._split_by_separator(text, separator, depth)
                if chunks:
                    return chunks

        # 如果所有分割符都无效，强制按字符分割
        return self._force_split(text)

    def _split_by_separator(self, text: str, separator: str, depth: int) -> List[str]:
        """使用指定分割符分割文本"""
        parts = text.split(separator)
        chunks = []
        current_chunk = ""

        for i, part in enumerate(parts):
            # 重新添加分割符（除了最后一个部分）
            if i < len(parts) - 1 and separator != "":
                part += separator

            # 检查当前块加上新部分是否超过大小限制
            if len(current_chunk) + len(part) <= self.chunk_size:
                current_chunk += part
            else:
                # 如果当前块不为空，添加到结果中
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())

                # 如果单个部分太大，递归分割
                if len(part) > self.chunk_size:
                    sub_chunks = self.split_text_recursive(part, depth + 1)
                    chunks.extend(sub_chunks)
                    current_chunk = ""
                else:
                    current_chunk = part

        # 添加最后一个块
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _force_split(self, text: str) -> List[str]:
        """强制按字符分割文本"""
        chunks = []
        for i in range(0, len(text), self.chunk_size):
            chunk = text[i:i + self.chunk_size]
            if chunk.strip():
                chunks.append(chunk.strip())
        return chunks

    def create_overlapping_chunks(self, chunks: List[str]) -> List[Dict[str, Any]]:
        """
        创建重叠分块

        Args:
            chunks: 基础分块列表

        Returns:
            包含重叠信息的分块列表
        """
        overlapping_chunks = []

        for i, chunk in enumerate(chunks):
            chunk_info = {
                'text': chunk,
                'index': i,
                'overlap_prev': '',
                'overlap_next': ''
            }

            # 添加与前一个块的重叠
            if i > 0:
                prev_chunk = chunks[i - 1]
                if len(prev_chunk) > self.overlap_size:
                    chunk_info['overlap_prev'] = prev_chunk[-self.overlap_size:]

            # 添加与后一个块的重叠
            if i < len(chunks) - 1:
                next_chunk = chunks[i + 1]
                if len(next_chunk) > self.overlap_size:
                    chunk_info['overlap_next'] = next_chunk[:self.overlap_size]

            # 创建完整的重叠文本
            full_text = chunk_info['overlap_prev'] + chunk + chunk_info['overlap_next']
            chunk_info['full_text'] = full_text

            overlapping_chunks.append(chunk_info)

        return overlapping_chunks

class PDFProcessor:
    """PDF高级处理类"""

    def __init__(self):
        self.vision_engine = vision_engine
        self.vision_engine_type = vision_engine_type

    def extract_text_with_pymupdf(self, file_content: bytes) -> str:
        """使用PyMuPDF提取PDF文本"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            # 打开PDF文档
            doc = fitz.open(temp_file_path)
            text_content = ""

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text_content += page.get_text() + "\n"

            doc.close()
            os.unlink(temp_file_path)  # 删除临时文件

            return text_content

        except Exception as e:
            raise ValueError(f"PyMuPDF文本提取失败: {str(e)}")

    def extract_tables_with_camelot(self, file_content: bytes) -> List[str]:
        """使用Camelot提取PDF表格"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            # 使用Camelot提取表格
            tables = camelot.read_pdf(temp_file_path, pages='all')
            table_texts = []

            for table in tables:
                if not table.df.empty:
                    # 将表格转换为文本
                    table_text = table.df.to_string(index=False)
                    table_texts.append(f"[表格]\n{table_text}")

            os.unlink(temp_file_path)  # 删除临时文件
            return table_texts

        except Exception as e:
            print(f"⚠️ Camelot表格提取失败: {e}")
            return []

    def extract_tables_with_pdfplumber(self, file_content: bytes) -> List[str]:
        """使用pdfplumber提取PDF表格"""
        try:
            pdf_file = BytesIO(file_content)
            table_texts = []

            with pdfplumber.open(pdf_file) as pdf:
                for page in pdf.pages:
                    tables = page.extract_tables()
                    for table in tables:
                        if table:
                            # 将表格转换为文本
                            table_rows = []
                            for row in table:
                                if row:
                                    clean_row = [str(cell) if cell else "" for cell in row]
                                    table_rows.append(" | ".join(clean_row))

                            if table_rows:
                                table_text = "\n".join(table_rows)
                                table_texts.append(f"[表格]\n{table_text}")

            return table_texts

        except Exception as e:
            print(f"⚠️ pdfplumber表格提取失败: {e}")
            return []

    def extract_images_and_recognize(self, file_content: bytes) -> List[str]:
        """使用PyMuPDF提取图片并用Qwen2.5-VL识别内容"""
        if not self.vision_engine:
            print("⚠️ Qwen2.5-VL引擎不可用，跳过图像识别")
            return []

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            # 打开PDF文档
            doc = fitz.open(temp_file_path)
            ocr_texts = []

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                image_list = page.get_images()

                for img_index, img in enumerate(image_list):
                    try:
                        # 提取图片
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)

                        # 转换为RGB格式
                        if pix.n - pix.alpha < 4:
                            img_data = pix.tobytes("png")

                            # 创建临时图片文件
                            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as img_temp:
                                img_temp.write(img_data)
                                img_temp_path = img_temp.name

                            # 使用Qwen2.5-VL识别图像内容
                            img_text = self._recognize_with_qwen2vl(img_temp_path)

                            if img_text.strip():
                                ocr_texts.append(f"[图片内容 - 第{page_num+1}页]\n{img_text.strip()}")

                            # 删除临时图片文件
                            os.unlink(img_temp_path)

                        pix = None

                    except Exception as e:
                        print(f"⚠️ 图片{img_index}处理失败: {e}")
                        continue

            doc.close()
            os.unlink(temp_file_path)  # 删除临时文件

            return ocr_texts

        except Exception as e:
            print(f"⚠️ 图片提取和识别失败: {e}")
            return []

    def _recognize_with_qwen2vl(self, image_path: str) -> str:
        """使用Qwen2.5-VL模型识别图像内容"""
        try:
            from PIL import Image

            # 加载图片
            image = Image.open(image_path)

            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path,
                        },
                        {"type": "text", "text": "请详细描述这张图片的内容，包括文字、图表、图像等所有信息。如果有文字，请准确识别出来。"},
                    ],
                }
            ]

            # 准备输入
            text = self.vision_engine['processor'].apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )

            # 处理图像输入
            try:
                # 使用 qwen_vl_utils 处理
                image_inputs, video_inputs = process_vision_info(messages)
                inputs = self.vision_engine['processor'](
                    text=[text],
                    images=image_inputs,
                    videos=video_inputs,
                    padding=True,
                    return_tensors="pt",
                )
            except:
                # 备选方案：直接处理图像
                inputs = self.vision_engine['processor'](
                    text=[text],
                    images=[image],
                    padding=True,
                    return_tensors="pt",
                )

            inputs = inputs.to(self.vision_engine['model'].device)

            # 生成文本
            generated_ids = self.vision_engine['model'].generate(**inputs, max_new_tokens=512)
            generated_ids_trimmed = [
                out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.vision_engine['processor'].batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )

            return output_text[0] if output_text else ""

        except Exception as e:
            print(f"⚠️ Qwen2.5-VL 图像识别失败: {e}")
            return ""

    def process_pdf_comprehensive(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """综合处理PDF文件"""
        print(f"📄 开始综合处理PDF: {filename}")

        extracted_content = {
            'text': '',
            'tables': [],
            'images_text': [],
            'total_chars': 0,
            'processing_info': {}
        }

        # 1. 提取文本内容
        try:
            text_content = self.extract_text_with_pymupdf(file_content)
            extracted_content['text'] = text_content
            extracted_content['processing_info']['text_extraction'] = 'PyMuPDF成功'
            print(f"📝 文本提取完成: {len(text_content)} 字符")
        except Exception as e:
            extracted_content['processing_info']['text_extraction'] = f'失败: {str(e)}'
            print(f"❌ 文本提取失败: {e}")

        # 2. 提取表格内容（优先使用Camelot，备选pdfplumber）
        try:
            tables = self.extract_tables_with_camelot(file_content)
            if not tables:
                tables = self.extract_tables_with_pdfplumber(file_content)
                extracted_content['processing_info']['table_extraction'] = 'pdfplumber'
            else:
                extracted_content['processing_info']['table_extraction'] = 'Camelot'

            extracted_content['tables'] = tables
            print(f"📊 表格提取完成: {len(tables)} 个表格")
        except Exception as e:
            extracted_content['processing_info']['table_extraction'] = f'失败: {str(e)}'
            print(f"❌ 表格提取失败: {e}")

        # 3. 提取图片并用Qwen2.5-VL识别内容
        try:
            images_text = self.extract_images_and_recognize(file_content)
            extracted_content['images_text'] = images_text
            extracted_content['processing_info']['vision_extraction'] = f'成功识别{len(images_text)}个图片'
            print(f"🖼️ 图片内容识别完成: {len(images_text)} 个图片")
        except Exception as e:
            extracted_content['processing_info']['vision_extraction'] = f'失败: {str(e)}'
            print(f"❌ 图片内容识别失败: {e}")

        # 4. 合并所有内容
        full_content = ""

        # 添加文本内容
        if extracted_content['text']:
            full_content += extracted_content['text'] + "\n\n"

        # 添加表格内容
        for table in extracted_content['tables']:
            full_content += table + "\n\n"

        # 添加图片文字内容
        for img_text in extracted_content['images_text']:
            full_content += img_text + "\n\n"

        extracted_content['full_content'] = full_content.strip()
        extracted_content['total_chars'] = len(full_content)

        print(f"✅ PDF综合处理完成: 总计 {extracted_content['total_chars']} 字符")
        return extracted_content

def extract_and_chunk_pdf(file_content: bytes, filename: str) -> List[Dict[str, Any]]:
    """
    提取PDF内容并进行高级分块处理

    Args:
        file_content: 文件二进制内容
        filename: 文件名

    Returns:
        分块信息列表，每个元素包含文本和元数据
    """
    try:
        # 1. 综合提取PDF内容
        extracted_data = pdf_processor.process_pdf_comprehensive(file_content, filename)

        if not extracted_data['full_content'].strip():
            raise ValueError("PDF文件中没有提取到有效内容")

        # 2. 进行递归分块
        print(f"🔪 开始分块处理PDF: {filename}")
        print(f"📊 PDF总长度: {extracted_data['total_chars']} 字符")

        # 第一步：递归分块
        basic_chunks = document_chunker.split_text_recursive(extracted_data['full_content'])
        print(f"🔪 递归分块完成，共 {len(basic_chunks)} 个基础块")

        # 第二步：创建重叠分块
        overlapping_chunks = document_chunker.create_overlapping_chunks(basic_chunks)
        print(f"🔗 重叠分块完成，共 {len(overlapping_chunks)} 个重叠块")

        # 第三步：添加PDF特有的元数据
        processed_chunks = []
        for i, chunk_info in enumerate(overlapping_chunks):
            processed_chunk = {
                'text': chunk_info['full_text'],
                'original_text': chunk_info['text'],
                'chunk_index': i,
                'total_chunks': len(overlapping_chunks),
                'source_file': filename,
                'source_type': 'pdf',
                'chunk_size': len(chunk_info['full_text']),
                'has_overlap': bool(chunk_info['overlap_prev'] or chunk_info['overlap_next']),
                'metadata': {
                    'overlap_prev_size': len(chunk_info['overlap_prev']),
                    'overlap_next_size': len(chunk_info['overlap_next']),
                    'original_size': len(chunk_info['text']),
                    'pdf_processing_info': extracted_data['processing_info'],
                    'content_types': {
                        'has_text': bool(extracted_data['text']),
                        'has_tables': bool(extracted_data['tables']),
                        'has_images': bool(extracted_data['images_text']),
                        'table_count': len(extracted_data['tables']),
                        'image_count': len(extracted_data['images_text'])
                    }
                }
            }
            processed_chunks.append(processed_chunk)

        print(f"✅ PDF分块处理完成: {len(processed_chunks)} 个最终块")
        return processed_chunks

    except Exception as e:
        raise ValueError(f"PDF分块处理失败: {str(e)}")

def extract_and_chunk_docx(file_content: bytes, filename: str) -> List[Dict[str, Any]]:
    """
    提取Word文档内容并进行高级分块处理

    Args:
        file_content: 文件二进制内容
        filename: 文件名

    Returns:
        分块信息列表，每个元素包含文本和元数据
    """
    try:
        doc_file = BytesIO(file_content)
        doc = docx.Document(doc_file)

        # 提取文档结构化内容
        document_content = {
            'title': '',
            'paragraphs': [],
            'tables': [],
            'headers': [],
            'footers': []
        }

        # 提取段落内容
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                # 检测标题级别
                style_name = paragraph.style.name.lower()
                is_heading = 'heading' in style_name or 'title' in style_name

                para_info = {
                    'text': paragraph.text.strip(),
                    'style': paragraph.style.name,
                    'is_heading': is_heading,
                    'level': 0
                }

                # 提取标题级别
                if is_heading:
                    level_match = re.search(r'(\d+)', style_name)
                    if level_match:
                        para_info['level'] = int(level_match.group(1))

                document_content['paragraphs'].append(para_info)

        # 提取表格内容
        for table in doc.tables:
            table_text = []
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    table_text.append(" | ".join(row_text))

            if table_text:
                document_content['tables'].append({
                    'text': "\n".join(table_text),
                    'type': 'table'
                })

        # 合并所有文本内容
        full_text = ""

        # 添加段落内容
        for para in document_content['paragraphs']:
            if para['is_heading']:
                full_text += f"\n\n{para['text']}\n"
            else:
                full_text += f"{para['text']}\n"

        # 添加表格内容
        for table in document_content['tables']:
            full_text += f"\n\n[表格]\n{table['text']}\n"

        # 进行递归分块
        print(f"📄 开始分块处理文档: {filename}")
        print(f"📊 文档总长度: {len(full_text)} 字符")

        # 第一步：递归分块
        basic_chunks = document_chunker.split_text_recursive(full_text.strip())
        print(f"🔪 递归分块完成，共 {len(basic_chunks)} 个基础块")

        # 第二步：创建重叠分块
        overlapping_chunks = document_chunker.create_overlapping_chunks(basic_chunks)
        print(f"🔗 重叠分块完成，共 {len(overlapping_chunks)} 个重叠块")

        # 第三步：添加文档元数据
        processed_chunks = []
        for i, chunk_info in enumerate(overlapping_chunks):
            processed_chunk = {
                'text': chunk_info['full_text'],
                'original_text': chunk_info['text'],
                'chunk_index': i,
                'total_chunks': len(overlapping_chunks),
                'source_file': filename,
                'source_type': 'docx',
                'chunk_size': len(chunk_info['full_text']),
                'has_overlap': bool(chunk_info['overlap_prev'] or chunk_info['overlap_next']),
                'metadata': {
                    'overlap_prev_size': len(chunk_info['overlap_prev']),
                    'overlap_next_size': len(chunk_info['overlap_next']),
                    'original_size': len(chunk_info['text'])
                }
            }
            processed_chunks.append(processed_chunk)

        print(f"✅ Word文档分块处理完成: {len(processed_chunks)} 个最终块")
        return processed_chunks

    except Exception as e:
        raise ValueError(f"Word文档处理失败: {str(e)}")

def extract_text_from_file(file):
    """从不同格式的文件中提取文本"""
    filename = file.filename.lower()
    file_content = file.read()

    try:
        if filename.endswith('.txt'):
            # 处理文本文件
            try:
                return file_content.decode('utf-8')
            except UnicodeDecodeError:
                return file_content.decode('gbk')

        elif filename.endswith('.pdf'):
            # 处理PDF文件 - 使用高级综合处理
            # 注意：这里返回特殊标记，表示需要特殊处理
            return "PDF_CHUNKED_PROCESSING"

        elif filename.endswith(('.doc', '.docx')):
            # 处理Word文档 - 使用高级分块处理
            # 注意：这里返回特殊标记，表示需要特殊处理
            return "DOCX_CHUNKED_PROCESSING"

        elif filename.endswith(('.csv')):
            # 处理CSV文件
            csv_file = BytesIO(file_content)
            try:
                # 尝试不同的编码
                try:
                    df = pd.read_csv(csv_file, encoding='utf-8')
                except UnicodeDecodeError:
                    csv_file.seek(0)
                    df = pd.read_csv(csv_file, encoding='gbk')

                text = ""
                # 处理列名
                text += " ".join(str(col) for col in df.columns) + "\n"
                # 处理数据行
                for _, row in df.iterrows():
                    row_text = " ".join(str(value) for value in row.values if pd.notna(value))
                    if row_text.strip():
                        text += row_text + "\n"
                return text
            except Exception as e:
                raise ValueError(f"CSV文件处理失败: {str(e)}")

        elif filename.endswith(('.xlsx', '.xls')):
            # 处理Excel文件 - 使用最安全的方法
            print(f"📊 开始处理Excel文件: {filename}")

            # 直接使用最安全的openpyxl方法，避开pandas的潜在问题
            try:
                text = _extract_excel_with_openpyxl_direct(file_content)
                if text.strip():
                    print(f"✅ Excel文件处理成功: {len(text)} 字符")
                    return text
                else:
                    raise ValueError("Excel文件中没有找到有效数据")
            except Exception as e:
                print(f"❌ Excel文件处理失败: {e}")
                raise ValueError(f"Excel文件处理失败: {str(e)}。请确保文件格式正确且包含有效数据。")

        else:
            # 尝试作为文本文件处理
            try:
                return file_content.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    return file_content.decode('gbk')
                except UnicodeDecodeError:
                    raise ValueError(f"不支持的文件格式: {filename}")

    except Exception as e:
        raise ValueError(f"文件处理错误: {str(e)}")

def _convert_dataframe_to_text(df) -> str:
    """将DataFrame转换为文本"""
    text = ""

    # 处理列名
    if hasattr(df, 'columns'):
        text += " ".join(str(col) for col in df.columns) + "\n"

    # 处理数据行
    for _, row in df.iterrows():
        row_values = []
        for value in row.values:
            if pd.notna(value) and str(value).strip():
                row_values.append(str(value))
        if row_values:
            text += " ".join(row_values) + "\n"

    return text

def _extract_excel_with_openpyxl_direct(file_content: bytes) -> str:
    """直接使用openpyxl提取Excel内容"""
    try:
        excel_file = BytesIO(file_content)
        workbook = load_workbook(excel_file, read_only=True, data_only=True)

        text = ""

        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]

            # 获取数据范围
            if worksheet.max_row == 1 and worksheet.max_column == 1:
                continue

            text += f"工作表: {sheet_name}\n"

            # 读取数据
            for row in worksheet.iter_rows(values_only=True):
                row_text = []
                for cell in row:
                    if cell is not None and str(cell).strip():
                        row_text.append(str(cell).strip())

                if row_text:
                    text += " ".join(row_text) + "\n"

            text += "\n"

        return text

    except Exception as e:
        raise ValueError(f"openpyxl直接提取失败: {str(e)}")

def get_supported_formats():
    """获取支持的文件格式列表"""
    formats = ['.txt', '.pdf', '.doc', '.docx', '.csv', '.xlsx', '.xls']
    return formats

class ExcelProcessor:
    """Excel高级处理类"""

    def __init__(self):
        self.chunk_size = 300  # Excel专用分块大小
        self.overlap_size = 30  # Excel专用重叠大小

    def parse_excel_with_pandas(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """使用pandas解析Excel文件，包含多种备选方案"""
        extracted_content = {
            'sheets': {},
            'total_rows': 0,
            'total_cols': 0,
            'sheet_count': 0,
            'processing_info': {}
        }

        print(f"📊 开始解析Excel文件: {filename}")

        # 尝试多种引擎和方法
        engines_to_try = ['openpyxl', 'xlrd', None]

        for engine in engines_to_try:
            try:
                excel_file = BytesIO(file_content)

                # 根据引擎选择不同的参数
                read_params = {'sheet_name': None}
                if engine:
                    read_params['engine'] = engine

                print(f"🔧 尝试使用引擎: {engine or 'auto'}")

                # 读取所有工作表
                excel_data = pd.read_excel(excel_file, **read_params)

                # 如果成功读取，处理数据
                for sheet_name, df in excel_data.items():
                    if df.empty:
                        print(f"⚠️ 工作表 {sheet_name} 为空，跳过")
                        continue

                    print(f"📋 处理工作表: {sheet_name} ({df.shape[0]}行 x {df.shape[1]}列)")

                    try:
                        # 清理数据
                        df = df.dropna(how='all')  # 删除全空行
                        df = df.dropna(axis=1, how='all')  # 删除全空列

                        # 填充NaN值
                        df = df.fillna('')

                        sheet_info = {
                            'name': sheet_name,
                            'shape': df.shape,
                            'columns': df.columns.tolist(),
                            'data': df,
                            'has_header': True,
                            'data_types': df.dtypes.to_dict() if hasattr(df, 'dtypes') else {}
                        }

                        extracted_content['sheets'][sheet_name] = sheet_info
                        extracted_content['total_rows'] += df.shape[0]
                        extracted_content['total_cols'] += df.shape[1]

                    except Exception as sheet_error:
                        print(f"⚠️ 处理工作表 {sheet_name} 时出错: {sheet_error}")
                        continue

                extracted_content['sheet_count'] = len(extracted_content['sheets'])
                extracted_content['processing_info']['pandas_engine'] = engine or 'auto'
                extracted_content['processing_info']['status'] = 'success'

                print(f"✅ Excel解析完成: {extracted_content['sheet_count']} 个工作表，总计 {extracted_content['total_rows']} 行")
                return extracted_content

            except Exception as e:
                print(f"⚠️ 引擎 {engine or 'auto'} 失败: {str(e)}")
                if engine == engines_to_try[-1]:  # 最后一个引擎也失败了
                    # 尝试简化的处理方式
                    return self._fallback_excel_processing(file_content, filename)
                continue

        # 如果所有引擎都失败，抛出错误
        raise ValueError("所有Excel解析引擎都失败了")

    def _fallback_excel_processing(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """备选的Excel处理方法，使用更简单的方式"""
        print("🔄 使用备选Excel处理方法...")

        try:
            # 尝试使用openpyxl直接读取
            excel_file = BytesIO(file_content)
            workbook = load_workbook(excel_file, read_only=True, data_only=True)

            extracted_content = {
                'sheets': {},
                'total_rows': 0,
                'total_cols': 0,
                'sheet_count': 0,
                'processing_info': {'method': 'openpyxl_direct', 'status': 'fallback'}
            }

            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]

                # 获取数据范围
                if worksheet.max_row == 1 and worksheet.max_column == 1:
                    print(f"⚠️ 工作表 {sheet_name} 似乎为空，跳过")
                    continue

                print(f"📋 处理工作表: {sheet_name} (openpyxl直接读取)")

                # 读取数据
                data_rows = []
                headers = []

                for row_idx, row in enumerate(worksheet.iter_rows(values_only=True), 1):
                    if row_idx == 1:
                        # 第一行作为标题
                        headers = [str(cell) if cell is not None else f"Column_{i}" for i, cell in enumerate(row)]
                    else:
                        # 数据行
                        row_data = [str(cell) if cell is not None else "" for cell in row]
                        if any(cell.strip() for cell in row_data):  # 只保留非空行
                            data_rows.append(row_data)

                if not data_rows:
                    print(f"⚠️ 工作表 {sheet_name} 没有有效数据，跳过")
                    continue

                # 创建简化的DataFrame结构
                sheet_info = {
                    'name': sheet_name,
                    'shape': (len(data_rows), len(headers)),
                    'columns': headers,
                    'data_rows': data_rows,  # 使用原始数据而不是DataFrame
                    'has_header': True,
                    'processing_method': 'openpyxl_direct'
                }

                extracted_content['sheets'][sheet_name] = sheet_info
                extracted_content['total_rows'] += len(data_rows)
                extracted_content['total_cols'] += len(headers)

            extracted_content['sheet_count'] = len(extracted_content['sheets'])

            print(f"✅ 备选Excel解析完成: {extracted_content['sheet_count']} 个工作表，总计 {extracted_content['total_rows']} 行")
            return extracted_content

        except Exception as e:
            print(f"❌ 备选Excel处理也失败: {e}")
            # 最后的备选方案：返回基本结构
            return {
                'sheets': {},
                'total_rows': 0,
                'total_cols': 0,
                'sheet_count': 0,
                'processing_info': {'method': 'failed', 'error': str(e)}
            }

    def streaming_chunk(self, text: str) -> List[str]:
        """流式分块：按固定大小分块"""
        if not text or len(text) <= self.chunk_size:
            return [text] if text.strip() else []

        chunks = []
        start = 0

        while start < len(text):
            end = start + self.chunk_size

            # 如果不是最后一块，尝试在合适位置断开
            if end < len(text):
                # 寻找最近的分隔符
                for sep in ['\n', '。', '，', ' ']:
                    sep_pos = text.rfind(sep, start, end)
                    if sep_pos > start:
                        end = sep_pos + 1
                        break

            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)

            # 添加重叠
            start = end - self.overlap_size if end < len(text) else end

        return chunks

    def row_column_chunk(self, sheet_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """按行列分块：将Excel数据按行和列进行分块，支持不同数据格式"""
        sheet_name = sheet_data['name']
        columns = sheet_data['columns']
        chunks = []

        # 检查数据格式并按行分块
        if 'data' in sheet_data and hasattr(sheet_data['data'], 'iterrows'):
            # pandas DataFrame格式
            df = sheet_data['data']
            for idx, row in df.iterrows():
                row_text = ""

                # 添加列名和值的对应关系
                for col_name, value in row.items():
                    if pd.notna(value) and str(value).strip():
                        row_text += f"{col_name}: {str(value).strip()}\n"

                if row_text.strip():
                    chunks.append({
                        'text': row_text.strip(),
                        'type': 'row',
                        'sheet': sheet_name,
                        'row_index': idx,
                        'metadata': {
                            'chunk_type': 'row_based',
                            'source_row': idx,
                            'sheet_name': sheet_name
                        }
                    })

        elif 'data_rows' in sheet_data:
            # 原始数据行格式（备选方案）
            data_rows = sheet_data['data_rows']
            for idx, row_data in enumerate(data_rows):
                row_text = ""

                # 添加列名和值的对应关系
                for i, value in enumerate(row_data):
                    if value and str(value).strip():
                        col_name = columns[i] if i < len(columns) else f"Column_{i}"
                        row_text += f"{col_name}: {str(value).strip()}\n"

                if row_text.strip():
                    chunks.append({
                        'text': row_text.strip(),
                        'type': 'row',
                        'sheet': sheet_name,
                        'row_index': idx,
                        'metadata': {
                            'chunk_type': 'row_based',
                            'source_row': idx,
                            'sheet_name': sheet_name
                        }
                    })

        # 方法2：按列分块（每列作为一个块）
        for col_name in df.columns:
            col_values = df[col_name].dropna()
            if not col_values.empty:
                col_text = f"列名: {col_name}\n"
                col_text += f"数据类型: {df[col_name].dtype}\n"
                col_text += "数据内容:\n"

                for idx, value in col_values.items():
                    if str(value).strip():
                        col_text += f"- {str(value).strip()}\n"

                chunks.append({
                    'text': col_text.strip(),
                    'type': 'column',
                    'sheet': sheet_name,
                    'column': col_name,
                    'metadata': {
                        'chunk_type': 'column_based',
                        'source_column': col_name,
                        'sheet_name': sheet_name,
                        'value_count': len(col_values)
                    }
                })

        # 方法3：按区块分块（将表格分成小区块）
        rows_per_block = 5
        cols_per_block = 3

        for row_start in range(0, len(df), rows_per_block):
            for col_start in range(0, len(df.columns), cols_per_block):
                row_end = min(row_start + rows_per_block, len(df))
                col_end = min(col_start + cols_per_block, len(df.columns))

                block_df = df.iloc[row_start:row_end, col_start:col_end]

                if not block_df.empty:
                    block_text = f"数据区块 [{row_start}:{row_end}, {col_start}:{col_end}]\n"
                    block_text += f"工作表: {sheet_name}\n\n"

                    # 添加列名
                    block_text += " | ".join(block_df.columns) + "\n"
                    block_text += "-" * (len(" | ".join(block_df.columns))) + "\n"

                    # 添加数据行
                    for _, row in block_df.iterrows():
                        row_values = []
                        for value in row.values:
                            if pd.notna(value):
                                row_values.append(str(value).strip())
                            else:
                                row_values.append("")
                        block_text += " | ".join(row_values) + "\n"

                    chunks.append({
                        'text': block_text.strip(),
                        'type': 'block',
                        'sheet': sheet_name,
                        'block_range': f"[{row_start}:{row_end}, {col_start}:{col_end}]",
                        'metadata': {
                            'chunk_type': 'block_based',
                            'row_range': (row_start, row_end),
                            'col_range': (col_start, col_end),
                            'sheet_name': sheet_name
                        }
                    })

        return chunks

    def semantic_chunk(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """语义分块：基于内容语义进行智能分块"""
        semantic_chunks = []

        for chunk in chunks:
            text = chunk['text']
            chunk_type = chunk['type']

            # 对文本进行语义分块
            if len(text) > self.chunk_size:
                # 使用递归分块器进行语义分块
                sub_chunks = document_chunker.split_text_recursive(text)

                for i, sub_text in enumerate(sub_chunks):
                    semantic_chunk = {
                        'text': sub_text,
                        'type': f"{chunk_type}_semantic",
                        'sheet': chunk['sheet'],
                        'parent_chunk': chunk.get('row_index', chunk.get('column', chunk.get('block_range', 'unknown'))),
                        'sub_index': i,
                        'metadata': {
                            **chunk['metadata'],
                            'semantic_split': True,
                            'parent_type': chunk_type,
                            'sub_chunk_index': i,
                            'total_sub_chunks': len(sub_chunks)
                        }
                    }
                    semantic_chunks.append(semantic_chunk)
            else:
                # 文本较短，直接使用
                chunk['metadata']['semantic_split'] = False
                semantic_chunks.append(chunk)

        return semantic_chunks

    def process_excel_comprehensive(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Excel综合处理：解析 → 流式分块 → 行列分块 → 语义分块"""
        print(f"📊 开始Excel综合处理: {filename}")

        # 第一步：使用pandas解析Excel
        excel_data = self.parse_excel_with_pandas(file_content, filename)

        all_chunks = []
        processing_stats = {
            'total_sheets': excel_data['sheet_count'],
            'total_rows': excel_data['total_rows'],
            'streaming_chunks': 0,
            'row_column_chunks': 0,
            'semantic_chunks': 0,
            'final_chunks': 0
        }

        for sheet_name, sheet_data in excel_data['sheets'].items():
            print(f"📋 处理工作表: {sheet_name}")

            # 第二步：流式分块（将整个工作表转为文本后进行流式分块）
            sheet_text = self._convert_sheet_to_text(sheet_data)
            streaming_chunks = self.streaming_chunk(sheet_text)
            processing_stats['streaming_chunks'] += len(streaming_chunks)

            print(f"🌊 流式分块完成: {len(streaming_chunks)} 个块")

            # 第三步：按行列分块
            row_column_chunks = self.row_column_chunk(sheet_data)
            processing_stats['row_column_chunks'] += len(row_column_chunks)

            print(f"📏 行列分块完成: {len(row_column_chunks)} 个块")

            # 第四步：语义分块
            semantic_chunks = self.semantic_chunk(row_column_chunks)
            processing_stats['semantic_chunks'] += len(semantic_chunks)

            print(f"🧠 语义分块完成: {len(semantic_chunks)} 个块")

            # 合并所有分块
            all_chunks.extend(semantic_chunks)

        processing_stats['final_chunks'] = len(all_chunks)

        result = {
            'chunks': all_chunks,
            'excel_data': excel_data,
            'processing_stats': processing_stats,
            'filename': filename,
            'total_chunks': len(all_chunks)
        }

        print(f"✅ Excel综合处理完成: {len(all_chunks)} 个最终分块")
        return result

    def _convert_sheet_to_text(self, sheet_data: Dict[str, Any]) -> str:
        """将工作表转换为文本，支持不同的数据格式"""
        sheet_name = sheet_data['name']

        text = f"工作表: {sheet_name}\n"
        text += f"数据维度: {sheet_data['shape'][0]}行 x {sheet_data['shape'][1]}列\n\n"

        # 添加列名
        columns = sheet_data['columns']
        text += "列名: " + " | ".join(columns) + "\n"
        text += "-" * 50 + "\n"

        # 检查数据格式
        if 'data' in sheet_data and hasattr(sheet_data['data'], 'iterrows'):
            # pandas DataFrame格式
            df = sheet_data['data']
            for _, row in df.iterrows():
                row_text = []
                for col_name, value in row.items():
                    if pd.notna(value) and str(value).strip():
                        row_text.append(f"{col_name}:{str(value).strip()}")

                if row_text:
                    text += " | ".join(row_text) + "\n"

        elif 'data_rows' in sheet_data:
            # 原始数据行格式（备选方案）
            data_rows = sheet_data['data_rows']
            for row_data in data_rows:
                row_text = []
                for i, value in enumerate(row_data):
                    if value and str(value).strip():
                        col_name = columns[i] if i < len(columns) else f"Column_{i}"
                        row_text.append(f"{col_name}:{str(value).strip()}")

                if row_text:
                    text += " | ".join(row_text) + "\n"

        return text

def extract_and_chunk_excel(file_content: bytes, filename: str) -> List[Dict[str, Any]]:
    """
    提取Excel内容并进行高级分块处理

    Args:
        file_content: 文件二进制内容
        filename: 文件名

    Returns:
        分块信息列表，每个元素包含文本和元数据
    """
    try:
        print(f"🚀 开始Excel分块处理: {filename}")

        # 基本文件验证
        if len(file_content) == 0:
            raise ValueError("Excel文件内容为空")

        if len(file_content) < 100:  # Excel文件通常至少几KB
            raise ValueError("Excel文件内容过小，可能不是有效的Excel文件")

        # 使用Excel处理器进行综合处理
        excel_result = excel_processor.process_excel_comprehensive(file_content, filename)

        if not excel_result.get('chunks'):
            # 如果没有分块，尝试简单的文本提取
            print("⚠️ 高级分块失败，尝试简单文本提取...")
            return _simple_excel_text_extraction(file_content, filename)

        # 转换为标准格式
        processed_chunks = []
        for i, chunk_info in enumerate(excel_result['chunks']):
            try:
                processed_chunk = {
                    'text': chunk_info.get('text', ''),
                    'chunk_index': i,
                    'total_chunks': excel_result.get('total_chunks', len(excel_result['chunks'])),
                    'source_file': filename,
                    'source_type': 'excel',
                    'chunk_size': len(chunk_info.get('text', '')),
                    'has_overlap': chunk_info.get('has_overlap', False),
                    'metadata': {
                        **chunk_info.get('metadata', {}),
                        'excel_processing_stats': excel_result.get('processing_stats', {}),
                        'sheet_info': {
                            'sheet_name': chunk_info.get('sheet', 'Unknown'),
                            'chunk_type': chunk_info.get('type', 'unknown')
                        }
                    }
                }
                processed_chunks.append(processed_chunk)
            except Exception as chunk_error:
                print(f"⚠️ 处理分块 {i} 时出错: {chunk_error}")
                continue

        if not processed_chunks:
            raise ValueError("所有分块处理都失败了")

        print(f"✅ Excel分块处理完成: {len(processed_chunks)} 个最终块")
        return processed_chunks

    except Exception as e:
        error_msg = f"Excel分块处理失败: {str(e)}"
        print(f"❌ {error_msg}")

        # 尝试最后的备选方案
        try:
            print("🔄 尝试最后的备选方案...")
            return _simple_excel_text_extraction(file_content, filename)
        except Exception as fallback_error:
            final_error = f"{error_msg}。备选方案也失败: {str(fallback_error)}"
            raise ValueError(final_error)

def _simple_excel_text_extraction(file_content: bytes, filename: str) -> List[Dict[str, Any]]:
    """
    简单的Excel文本提取，作为最后的备选方案
    完全避开pandas，直接使用openpyxl
    """
    print("🔧 使用简单Excel文本提取（openpyxl直接方式）...")

    try:
        # 直接使用openpyxl，避开pandas的潜在问题
        excel_file = BytesIO(file_content)

        # 使用openpyxl直接读取
        workbook = load_workbook(excel_file, read_only=True, data_only=True)

        text_content = ""

        # 处理所有工作表
        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]

            # 检查工作表是否为空
            if worksheet.max_row <= 1 and worksheet.max_column <= 1:
                continue

            text_content += f"工作表: {sheet_name}\n"
            text_content += "-" * 30 + "\n"

            # 读取所有行
            row_count = 0
            for row in worksheet.iter_rows(values_only=True):
                if row and any(cell is not None and str(cell).strip() for cell in row):
                    row_text = []
                    for cell in row:
                        if cell is not None and str(cell).strip():
                            row_text.append(str(cell).strip())

                    if row_text:
                        text_content += " | ".join(row_text) + "\n"
                        row_count += 1

            if row_count > 0:
                text_content += f"\n处理了 {row_count} 行数据\n\n"

        if not text_content.strip():
            raise ValueError("Excel文件中没有找到有效数据")

        # 使用基础分块器进行分块
        chunks = document_chunker.split_text_recursive(text_content)

        # 转换为标准格式
        processed_chunks = []
        for i, chunk_text in enumerate(chunks):
            processed_chunk = {
                'text': chunk_text,
                'chunk_index': i,
                'total_chunks': len(chunks),
                'source_file': filename,
                'source_type': 'excel',
                'chunk_size': len(chunk_text),
                'has_overlap': False,
                'metadata': {
                    'processing_method': 'openpyxl_direct_simple',
                    'sheet_info': {
                        'sheet_name': 'Multiple',
                        'chunk_type': 'simple_text'
                    }
                }
            }
            processed_chunks.append(processed_chunk)

        print(f"✅ 简单Excel提取完成: {len(processed_chunks)} 个块")
        return processed_chunks

    except ImportError:
        # 如果openpyxl也不可用，返回基本错误信息
        raise ValueError("Excel处理库不可用，请安装 openpyxl: pip install openpyxl")
    except Exception as e:
        raise ValueError(f"简单Excel提取失败: {str(e)}")

# 初始化文档分块器、PDF处理器和Excel处理器
document_chunker = DocumentChunker(chunk_size=500, overlap_size=50, max_depth=3)
pdf_processor = PDFProcessor()
excel_processor = ExcelProcessor()