#!/usr/bin/env python3
"""
FAISS向量搜索聊天系统启动脚本
"""

import sys
import os

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'flask',
        'openai', 
        'faiss',
        'sentence_transformers',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'faiss':
                import faiss
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_api_key():
    """检查API密钥配置"""
    try:
        from KEY.DEEPSEEK_KEY import deepseek_key
        if deepseek_key and deepseek_key.startswith('sk-'):
            print("✅ DeepSeek API密钥配置正确")
            return True
        else:
            print("❌ DeepSeek API密钥配置错误")
            return False
    except ImportError:
        print("❌ 找不到API密钥配置文件")
        return False

def main():
    """主函数"""
    print("🚀 启动FAISS向量搜索聊天系统...")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查API密钥
    if not check_api_key():
        print("\n请检查 KEY/DEEPSEEK_KEY.py 文件中的API密钥配置")
        sys.exit(1)
    
    print("\n🎉 系统检查通过，正在启动Web服务器...")
    print("📱 请在浏览器中访问: http://localhost:5000")
    print("🛑 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 启动Flask应用
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
