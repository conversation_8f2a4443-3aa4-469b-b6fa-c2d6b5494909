<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单文件上传与对话</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .upload-section, .chat-section {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            margin-top: 0;
            color: #333;
        }
        #file-input {
            margin-bottom: 10px;
        }
        #upload-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #upload-button:hover {
            background-color: #45a049;
        }
        #chat-messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 18px;
            max-width: 70%;
        }
        .user-message {
            background-color: #e3f2fd;
            margin-left: auto;
        }
        .bot-message {
            background-color: #f1f1f1;
            margin-right: auto;
        }
        #chat-input {
            width: calc(100% - 90px);
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        #send-button {
            width: 70px;
            padding: 10px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #send-button:hover {
            background-color: #0b7dda;
        }

        .loading {
            opacity: 0.7;
            font-style: italic;
        }
        .response-mode-section {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .response-mode-section label {
            margin-right: 20px;
            cursor: pointer;
            font-weight: normal;
        }
        .response-mode-section input[type="radio"] {
            margin-right: 5px;
        }
        .file-formats-info {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #e3f2fd;
            border-radius: 4px;
            border-left: 3px solid #2196F3;
        }
        .file-formats-info small {
            color: #1976d2;
            font-weight: 500;
        }
        #upload-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 0.9em;
            line-height: 1.4;
        }
        #upload-status:not(:empty) {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .chunking-info {
            background-color: #e8f5e8;
            border-left: 4px solid #4CAF50;
            padding: 8px 12px;
            margin-top: 8px;
            font-family: monospace;
            font-size: 0.85em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="upload-section">
            <h2>文件上传</h2>
            <div class="file-formats-info">
                <small>支持格式: TXT, PDF, DOC, DOCX, CSV, XLS, XLSX</small>
            </div>
            <input type="file" id="file-input" accept=".txt,.pdf,.doc,.docx,.csv,.xls,.xlsx">
            <button id="upload-button">上传文件</button>
            <div id="upload-status"></div>
        </div>
        
        <div class="chat-section">
            <h2>对话</h2>
            <div class="response-mode-section">
                <label>
                    <input type="radio" name="response-mode" value="concise" checked>
                    简洁回答
                </label>
                <label>
                    <input type="radio" name="response-mode" value="detailed">
                    详细回答
                </label>
            </div>
            <div id="chat-messages"></div>
            <div>
                <input type="text" id="chat-input" placeholder="输入消息...">
                <button id="send-button">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 文件上传功能
        document.getElementById('upload-button').addEventListener('click', function() {
            const fileInput = document.getElementById('file-input');
            const file = fileInput.files[0];

            if (!file) {
                document.getElementById('upload-status').textContent = '请先选择文件';
                return;
            }

            // 显示上传中状态
            document.getElementById('upload-status').textContent = '正在上传...';

            const formData = new FormData();
            formData.append('file', file);

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let statusText = data.message;

                    // 如果是Word文档的高级分块处理，显示详细信息
                    if (data.processing_type === 'advanced_chunking') {
                        statusText += `\n📊 Word文档详细信息:`;
                        statusText += `\n• 分块数量: ${data.chunks_added}`;
                        statusText += `\n• 总字符数: ${data.total_characters}`;
                        statusText += `\n• 平均块大小: ${data.average_chunk_size} 字符`;
                        statusText += `\n• 重叠块数量: ${data.overlapping_chunks}`;
                        statusText += `\n• 处理方式: 递归分块 + 重叠处理`;
                    } else if (data.processing_type === 'pdf_advanced_chunking') {
                        statusText += `\n📊 PDF文档详细信息:`;
                        statusText += `\n• 分块数量: ${data.chunks_added}`;
                        statusText += `\n• 总字符数: ${data.total_characters}`;
                        statusText += `\n• 平均块大小: ${data.average_chunk_size} 字符`;
                        statusText += `\n• 重叠块数量: ${data.overlapping_chunks}`;

                        // 显示内容分析
                        if (data.content_analysis) {
                            statusText += `\n📋 内容分析:`;
                            statusText += `\n  - 文本内容: ${data.content_analysis.has_text ? '✅' : '❌'}`;
                            statusText += `\n  - 表格数量: ${data.content_analysis.table_count || 0}`;
                            statusText += `\n  - 图片数量: ${data.content_analysis.image_count || 0}`;
                        }

                        // 显示处理信息
                        if (data.pdf_processing_info) {
                            statusText += `\n🔧 处理技术:`;
                            if (data.pdf_processing_info.text_extraction) {
                                statusText += `\n  - 文本提取: ${data.pdf_processing_info.text_extraction}`;
                            }
                            if (data.pdf_processing_info.table_extraction) {
                                statusText += `\n  - 表格提取: ${data.pdf_processing_info.table_extraction}`;
                            }
                            if (data.pdf_processing_info.ocr_extraction) {
                                statusText += `\n  - OCR识别: ${data.pdf_processing_info.ocr_extraction}`;
                            }
                        }

                        statusText += `\n• 处理方式: PyMuPDF + Camelot + PaddleOCR + 递归分块`;
                    } else if (data.processing_type === 'line_based') {
                        statusText += `\n📊 处理方式: 按行分割 (${data.lines_added} 行)`;
                    }

                    // 使用HTML格式显示，支持换行
                    const statusElement = document.getElementById('upload-status');
                    statusElement.innerHTML = statusText.replace(/\n/g, '<br>');
                    statusElement.style.whiteSpace = 'pre-line';

                    // 清空文件输入
                    fileInput.value = '';
                    // 更新统计信息
                    updateStats();
                } else {
                    document.getElementById('upload-status').textContent = '上传失败: ' + data.error;
                }
            })
            .catch(error => {
                document.getElementById('upload-status').textContent = '上传失败: ' + error;
            });
        });

        // 对话功能
        document.getElementById('send-button').addEventListener('click', sendMessage);
        document.getElementById('chat-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (message === '') return;

            // 添加用户消息到聊天框
            addMessage(message, 'user');
            input.value = '';

            // 显示正在思考状态
            const thinkingDiv = addMessage('正在思考...', 'bot');

            // 获取回答模式
            const responseMode = document.querySelector('input[name="response-mode"]:checked').value;

            // 发送消息到后端
            fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    mode: responseMode
                })
            })
            .then(response => response.json())
            .then(data => {
                // 移除思考状态
                thinkingDiv.remove();

                if (data.reply) {
                    addMessage(data.reply, 'bot');
                } else {
                    addMessage('抱歉，处理消息时出现错误: ' + data.error, 'bot');
                }
            })
            .catch(error => {
                // 移除思考状态
                thinkingDiv.remove();
                addMessage('网络错误: ' + error, 'bot');
            });
        }

        function addMessage(text, sender) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(sender + '-message');

            // 处理换行
            messageDiv.innerHTML = text.replace(/\n/g, '<br>');

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageDiv;
        }

        // 更新统计信息
        function updateStats() {
            fetch('/stats')
            .then(response => response.json())
            .then(data => {
                if (data.total_texts !== undefined) {
                    document.getElementById('upload-status').textContent +=
                        ` (数据库中共有 ${data.total_texts} 条文本)`;
                }
            })
            .catch(error => {
                console.log('获取统计信息失败:', error);
            });
        }

        // 页面加载时获取统计信息
        window.addEventListener('load', function() {
            updateStats();
            addMessage('欢迎使用FAISS向量搜索聊天系统！支持上传多种格式文件来扩充知识库。Word文档和PDF文档将使用高级分块技术进行智能处理：\n• Word: 递归分块+重叠处理\n• PDF: PyMuPDF文本提取+Camelot表格提取+PaddleOCR图片识别+递归分块\n提供更好的搜索效果。可以选择"简洁回答"或"详细回答"模式。', 'bot');
        });
    </script>
</body>
</html>